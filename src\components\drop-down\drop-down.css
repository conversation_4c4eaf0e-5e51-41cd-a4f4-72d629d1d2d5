/* 下拉框基础样式 */
.drop-down {
  position: relative;
  background: var(--color-bg-input);
  border-radius: var(--radius-m);
  box-sizing: border-box;
  cursor: pointer;
  user-select: none;
}

/* 下拉框尺寸变体 */
.drop-down--default .drop-down__selector {
  padding: 8px 12px;
  height: 32px;
}

.drop-down--small .drop-down__selector {
  padding: 6px 8px;
  height: 28px;
}

.drop-down--large .drop-down__selector {
  padding: 10px 16px;
  height: 44px;
}

/* 选择器区域 */
.drop-down__selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

/* 已选值 */
.drop-down__selected-value {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: var(--color-content-accent);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

/* 占位符 */
.drop-down__placeholder {
  color: var(--color-content-mute);
}

/* 箭头图标 */
.drop-down__icon {
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--color-content-mute);
  transition: transform 0.2s ease;
  margin-left: 8px;
}

.drop-down__icon--rotated {
  transform: rotate(180deg);
}

/* 选项列表容器 */
.drop-down__options {
  position: absolute;
  top: calc(100% + 4px);
  left: 0;
  width: 100%;
  max-height: 240px;
  overflow-y: auto;
  background: var(--color-bg-dialog);
  border-radius: var(--radius-m);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  z-index: 100;
  outline: 1px var(--color-border) solid;
  outline-offset: -1px;
}

/* 单个选项 */
.drop-down__option {
  padding: 8px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.2s ease;
  color: var(--color-content-accent);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.drop-down__option:hover:not(.drop-down__option--disabled) {
  background: var(--color-support);
}

/* 选中的选项 */
.drop-down__option--selected {
  color: var(--color-brand);
  font-weight: 500;
}

/* 选项文本 */
.drop-down__option-text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 选项图标 */
.drop-down__option-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 8px;
  color: var(--color-brand);
}

/* 禁用状态 */
.drop-down--disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.drop-down__option--disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* 打开状态 */
.drop-down--open {
  outline-color: var(--color-brand);
}

/* 滚动条样式 */
.drop-down__options::-webkit-scrollbar {
  width: 6px;
  background: transparent;
}

.drop-down__options::-webkit-scrollbar-thumb {
  background: var(--color-content-mute);
  border-radius: 3px;
}

/* 适配深色/浅色主题 */
.theme-light .drop-down__option:hover:not(.drop-down__option--disabled) {
  background: var(--color-support-hover);
}
