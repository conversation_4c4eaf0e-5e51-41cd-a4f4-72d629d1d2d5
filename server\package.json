{"name": "server", "version": "1.0.0", "main": "index.js", "dependencies": {"accepts": "^1.3.8", "anymatch": "^3.1.3", "append-field": "^1.0.0", "array-flatten": "^1.1.1", "balanced-match": "^1.0.2", "binary-extensions": "^2.3.0", "body-parser": "^1.20.3", "brace-expansion": "^1.1.11", "braces": "^3.0.3", "bson": "^5.5.1", "buffer-from": "^1.1.2", "busboy": "^1.6.0", "bytes": "^3.1.2", "call-bind-apply-helpers": "^1.0.2", "call-bound": "^1.0.4", "chokidar": "^3.6.0", "concat-map": "^0.0.1", "concat-stream": "^1.6.2", "content-disposition": "^0.5.4", "content-type": "^1.0.5", "cookie": "^0.7.1", "cookie-signature": "^1.0.6", "core-util-is": "^1.0.3", "cors": "^2.8.5", "debug": "^2.6.9", "depd": "^2.0.0", "destroy": "^1.2.0", "dotenv": "^16.5.0", "dunder-proto": "^1.0.1", "ee-first": "^1.1.1", "encodeurl": "^2.0.0", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "escape-html": "^1.0.3", "etag": "^1.8.1", "express": "^4.21.2", "fill-range": "^7.1.1", "finalhandler": "^1.3.1", "forwarded": "^0.2.0", "fresh": "^0.5.2", "function-bind": "^1.1.2", "get-intrinsic": "^1.3.0", "get-proto": "^1.0.1", "glob-parent": "^5.1.2", "gopd": "^1.2.0", "has-flag": "^3.0.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "http-errors": "^2.0.0", "iconv-lite": "^0.4.24", "ignore-by-default": "^1.0.1", "inherits": "^2.0.4", "ip-address": "^9.0.5", "ipaddr.js": "^1.9.1", "is-binary-path": "^2.1.0", "is-extglob": "^2.1.1", "is-glob": "^4.0.3", "is-number": "^7.0.0", "isarray": "^1.0.0", "jsbn": "^1.1.0", "kareem": "^2.5.1", "math-intrinsics": "^1.1.0", "media-typer": "^0.3.0", "memory-pager": "^1.5.0", "merge-descriptors": "^1.0.3", "methods": "^1.1.2", "mime": "^1.6.0", "mime-db": "^1.52.0", "mime-types": "^2.1.35", "minimatch": "^3.1.2", "minimist": "^1.2.8", "mkdirp": "^0.5.6", "mongodb": "^5.9.2", "mongodb-connection-string-url": "^2.6.0", "mongoose": "^7.8.7", "mpath": "^0.9.0", "mquery": "^5.0.0", "ms": "^2.0.0", "multer": "^1.4.5-lts.2", "negotiator": "^0.6.3", "nodemon": "^3.1.10", "normalize-path": "^3.0.0", "object-assign": "^4.1.1", "object-inspect": "^1.13.4", "on-finished": "^2.4.1", "parseurl": "^1.3.3", "path-to-regexp": "^0.1.12", "picomatch": "^2.3.1", "process-nextick-args": "^2.0.1", "proxy-addr": "^2.0.7", "pstree.remy": "^1.1.8", "punycode": "^2.3.1", "qs": "^6.13.0", "range-parser": "^1.2.1", "raw-body": "^2.5.2", "readable-stream": "^2.3.8", "readdirp": "^3.6.0", "safe-buffer": "^5.2.1", "safer-buffer": "^2.1.2", "semver": "^7.7.2", "send": "^0.19.0", "serve-static": "^1.16.2", "setprototypeof": "^1.2.0", "side-channel": "^1.1.0", "side-channel-list": "^1.0.0", "side-channel-map": "^1.0.1", "side-channel-weakmap": "^1.0.2", "sift": "^16.0.1", "simple-update-notifier": "^2.0.0", "smart-buffer": "^4.2.0", "socks": "^2.8.4", "sparse-bitfield": "^3.0.3", "sprintf-js": "^1.1.3", "sqlite3": "^5.1.7", "statuses": "^2.0.1", "streamsearch": "^1.1.0", "string_decoder": "^1.1.1", "supports-color": "^5.5.0", "to-regex-range": "^5.0.1", "toidentifier": "^1.0.1", "touch": "^3.1.1", "tr46": "^3.0.0", "type-is": "^1.6.18", "typedarray": "^0.0.6", "undefsafe": "^2.0.5", "undici-types": "^6.21.0", "unpipe": "^1.0.0", "util-deprecate": "^1.0.2", "utils-merge": "^1.0.1", "uuid": "^11.1.0", "vary": "^1.1.2", "webidl-conversions": "^7.0.0", "whatwg-url": "^11.0.0", "xtend": "^4.0.2"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": ""}