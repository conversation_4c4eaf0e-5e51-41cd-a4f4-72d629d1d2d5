:root {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
}

/* 主题样式 */
.app-container {
  width: 100%;
  max-width: 100%;
  margin: 0;
  padding: 0;
  transition: background-color 0.3s ease, color 0.3s ease;
  overflow-x: hidden;
}

.app-container.theme-dark {
  background-color: var(--color-page-background);
  color: var(--color-content-accent);
}

.app-container.theme-light {
  background-color: var(--color-page-background);
  color: var(--color-content-accent);
}

/* 头部样式 */
header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--color-border);
}

.app-container.light header {
  border-bottom-color: var(--color-border);
}

/* 展示区域样式 */
.demo-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  border-radius: 8px;
}

.dark .demo-section {
  background-color: rgba(255, 255, 255, 0.05);
}

.light .demo-section {
  background-color: rgba(0, 0, 0, 0.03);
}

.demo-section h2 {
  margin-bottom: 1rem;
  font-size: var(--font-size);
  font-weight: 500;
}

.demo-section h3 {
  margin-bottom: 0.5rem;
  font-size: var(--font-size);
  font-weight: 500;
  opacity: 0.85;
}

/* 滑块演示区域样式 */
.slider-demo {
  margin-bottom: 1.5rem;
  max-width: 500px;
}

.volume-slider {
  display: flex;
  align-items: center;
  gap: 16px;
}

.volume-slider svg {
  color: var(--color-content-accent);
}

/* 搜索框演示区域样式 */
.search-demo {
  margin-bottom: 1.5rem;
  max-width: 500px;
}

.search-demo h3 {
  margin-bottom: 12px;
}

.buttons-row {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

/* 计数器样式 */
.counter-demo {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.counter {
  font-size: var(--font-size);
  min-width: 2rem;
  text-align: center;
}

/* 通知图标样式 */
.notification-button {
  position: relative;
}

.badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: #f44336;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: var(--font-size);
  display: flex;
  align-items: center;
  justify-content: center;
}
